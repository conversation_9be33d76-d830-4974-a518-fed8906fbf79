# WhatsApp Verification System

This document explains how the WhatsApp verification system works in the SADEMY API.

## Overview

The system uses Twilio's WhatsApp API to send verification codes to users during registration. In development mode, messages are logged instead of actually sent.

## How It Works

### 1. User Registration
When a user registers via `POST /api/v1/auth/register`, the system:
- Creates a new user account (unverified)
- Generates a 6-digit verification code
- Stores the code in the `verification_codes` table
- Attempts to send the code via WhatsApp

### 2. WhatsApp Message Sending
The `WhatsAppService` handles message delivery:
- **Production**: Uses Twilio API to send actual WhatsApp messages
- **Development**: Logs messages to Laravel log file instead of sending

### 3. Phone Verification
Users verify their phone number via `POST /api/v1/auth/verify-code`:
- Validates the verification code
- Marks the user as verified
- Returns an authentication token

## Configuration

### Environment Variables
Add these to your `.env` file for production:

```env
# Twilio WhatsApp Configuration
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_WHATSAPP_FROM=whatsapp:+***********
```

### Development Mode
When Twilio credentials are not configured or `APP_ENV=local`, the system:
- Logs verification codes to `storage/logs/laravel.log`
- Still creates verification codes in the database
- Allows normal verification flow for testing

## API Endpoints

### Register User
```bash
POST /api/v1/auth/register
Content-Type: application/json

{
  "name": "John Doe",
  "phone": "+************",
  "password": "password123",
  "password_confirmation": "password123",
  "role": "user"
}
```

### Verify Phone
```bash
POST /api/v1/auth/verify-code
Content-Type: application/json

{
  "phone": "+************",
  "code": "123456"
}
```

### Resend Verification Code
```bash
POST /api/v1/auth/send-code
Content-Type: application/json

{
  "phone": "+************"
}
```

## Testing Commands

### View Verification Codes
```bash
# Show recent verification codes for all users
php artisan verification:show

# Show codes for specific phone number
php artisan verification:show "+************"
```

### Check Logs
```bash
# View WhatsApp messages in development
tail -f storage/logs/laravel.log | grep "WhatsApp message"
```

## Message Format

The verification message sent via WhatsApp:
```
Your SADEMY verification code is: 123456. This code will expire in 10 minutes.
```

## Security Features

- Verification codes expire after 10 minutes
- Codes are single-use (marked as used after verification)
- Phone numbers must be unique
- Users cannot login until phone is verified

## Troubleshooting

### Common Issues

1. **No verification code received**
   - Check if Twilio credentials are configured
   - Verify phone number format (include country code)
   - Check Laravel logs for error messages

2. **Code expired**
   - Request a new code via `/api/v1/auth/send-code`
   - Codes expire after 10 minutes

3. **Invalid code**
   - Ensure code is entered exactly as received
   - Check if code has already been used
   - Verify phone number matches registration

### Development Testing

In development mode, verification codes are logged. Check the log file:
```bash
grep "WhatsApp message" storage/logs/laravel.log
```

Example log entry:
```
[2025-07-22 16:17:53] local.INFO: WhatsApp message (local development - not sent via Twilio) 
{
  "phone": "+212600000005",
  "message": "Your SADEMY verification code is: 637425. This code will expire in 10 minutes.",
  "note": "This message was logged instead of sent because Twilio credentials are not configured or app is in local environment"
}
```
